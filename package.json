{"$schema": "https://www.raycast.com/schemas/extension.json", "name": "jet-hr-navigator", "title": "Jet HR Navigator", "description": "Helps navigation into Jet HR apps", "icon": "extension-icon.png", "author": "mirkoferrar<PERSON>", "platforms": ["macOS", "Windows"], "license": "MIT", "commands": [{"name": "jet-hr", "title": "Jet HR", "description": "", "mode": "view"}], "dependencies": {"@raycast/api": "^1.103.2", "@raycast/utils": "^1.17.0"}, "devDependencies": {"@raycast/eslint-config": "^2.0.4", "@types/node": "22.13.10", "@types/react": "19.0.10", "eslint": "^9.22.0", "prettier": "^3.5.3", "typescript": "^5.8.2"}, "scripts": {"build": "ray build", "dev": "ray develop", "fix-lint": "ray lint --fix", "lint": "ray lint", "prepublishOnly": "echo \"\\n\\nIt seems like you are trying to publish the Raycast extension to npm.\\n\\nIf you did intend to publish it to npm, remove the \\`prepublishOnly\\` script and rerun \\`npm publish\\` again.\\nIf you wanted to publish it to the Raycast Store instead, use \\`npm run publish\\` instead.\\n\\n\" && exit 1", "publish": "npx @raycast/api@latest publish"}}