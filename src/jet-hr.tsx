import { ActionPanel, LaunchProps, Action, List } from "@raycast/api";
import { useState } from "react";

import getSuggestions from "./suggestions";

export default function Command(props: LaunchProps) {
  const [searchText, setSearchText] = useState(props.fallbackText ?? "");
  const items = getSuggestions(searchText);
  return (
    <List searchBarPlaceholder="Search" onSearchTextChange={setSearchText}>
      {items.map((item, index) => (
        <List.Item
          key={index}
          icon={item.icon}
          title={item.title}
          subtitle={item.subtitle}
          actions={
            <ActionPanel>
              <Action.OpenInBrowser url={item.link} />
            </ActionPanel>
          }
        />
      ))}
    </List>
  );
}
