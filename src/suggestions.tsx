import { Icon } from "@raycast/api";

const ACTIONS = {
  GOTOURL: "GOTOURL",
};

const ACTION_KEYWORDS = {
  [ACTIONS.GOTOURL]: [],
};
const ENV = {
  LOCAL: "LOCAL",
  PROD: "PROD",
  STAGING: "STAGING",
};
const ENV_KEYWORDS = {
  [ENV.LOCAL]: ["loc", "local"],
  [ENV.PROD]: ["prod", "production"],
  [ENV.STAGING]: [new RegExp("stg\\d+"), new RegExp("staging\\d+")],
};

const TARGET = {
  SSO: "SSO",
  ADMIN: "ADMIN",
  BACKOFFICE: "BACKOFFICE",
  COMPANY: "COMPANY",
  DJANGO: "DJANG<PERSON>",
};
const TARGET_KEYWORDS = {
  [TARGET.SSO]: ["sso"],
  [TARGET.ADMIN]: ["admin"],
  [TARGET.BACKOFFICE]: ["bo", "backoffice"],
  [TARGET.COMPANY]: ["company"],
  [TARGET.DJANGO]: ["dj", "django"],
};

function popKeywords(nodes: string[], keywords: object, defaultKey: string): string[] {
  console.log("1", nodes.length);
  for (const nodeIndex in nodes) {
    const node = nodes[nodeIndex];
    for (const entry of Object.entries(keywords)) {
      const [keywordKey, keywordMatches] = entry;
      for (const keywordMatch of keywordMatches) {
        if ((keywordMatch instanceof RegExp && node.match(keywordMatch)) || node === keywordMatch) {
          nodes.splice(nodeIndex, 1);
          console.log("2", nodes.length);
          return [keywordKey, node];
        }
      }
    }
  }
  console.log(nodes.length);
  const defaultValue = keywords[defaultKey][0];
  return [defaultKey, defaultValue];
}

function getUrlSubdomain(target: string) {
  return (
    {
      [TARGET.SSO]: "sso",
      [TARGET.DJANGO]: "backend",
      [TARGET.ADMIN]: "admin",
      [TARGET.BACKOFFICE]: "backoffice",
      [TARGET.COMPANY]: "backoffice",
    }[target] || target
  );
}

function getUrlSubdirectory(target: string, nodes: string[]) {
  if (target === TARGET.DJANGO) {
    if (nodes.length === 0) {
      return "";
    }
    if (nodes.length === 2 && /^\d+$/.test(nodes[1])) {
      return `admin/jet/${nodes[0]}/${nodes[1]}/`;
    }
    return `admin/jet/${nodes[0]}/?q=${nodes.slice(1).join(" ")}`;
  }
  if (target === TARGET.COMPANY) {
    return `#/aziende/${nodes.join("")}`;
  }
  return "";
}

export default (searchText: string) => {
  const nodes = searchText.split(" ").filter((node) => node);
  const action = popKeywords(nodes, ACTION_KEYWORDS, ACTIONS.GOTOURL)[0];

  if (action === ACTIONS.GOTOURL) {
    const [envKW, envNode] = popKeywords(nodes, ENV_KEYWORDS, ENV.LOCAL);
    console.log("env", envKW, envNode);
    if (!envKW) {
      return [];
    }

    const target = popKeywords(nodes, TARGET_KEYWORDS, TARGET.SSO)[0];
    console.log("target", target);
    const urlSubdomain = getUrlSubdomain(target);
    const urlSubdirectory = getUrlSubdirectory(target, nodes);
    console.log("urlSubdomain", urlSubdomain);
    console.log("urlSubdirectory", urlSubdirectory);
    console.log("nodes", nodes);
    const url = {
      [ENV.LOCAL]: `http://${urlSubdomain}.jethr.localhost/${urlSubdirectory}`,
      [ENV.PROD]: `https://${urlSubdomain}.jethr.com/${urlSubdirectory}`,
      [ENV.STAGING]: `https://${urlSubdomain}.${envNode.slice(7)}.jethr.dev/${urlSubdirectory}`,
    }[envKW];
    console.log("url", url);

    return [
      {
        icon: Icon.Globe,
        title: "Go to url",
        subtitle: url,
        link: url,
      },
    ];
  }

  return [];
};
